import { create } from "zustand";
import { LAYOUT_HEIGHT, LAYOUT_WIDTH } from "../app/(main)/create-seat-map/lib/config";

export type NewItem =
  | "standing-section"
  | "seated-section"
  | "arc-section"
  | "polygon-section"
  | "circle-section"
  | "landmark"
  | "round-table"
  | "rectangle-table"
  | null;

type Dimensions = { w: number; h: number };
export type Position = { x: number; y: number };
type LayoutDimensions = {
  w: number;
  h: number;
  selected: boolean;
};

interface SeatMapStore {
  newItemView: NewItem;
  setNewItemView: (newItemView: NewItem) => void;
  editingItemId: string | null;
  setEditingItemId: (id: string | null) => void;
  stageDimensions: Dimensions;
  setStageDimensions: (dimensions: Dimensions) => void;
  layoutDimensions: LayoutDimensions;
  setLayoutDimensions: (dimensions: LayoutDimensions) => void;
  stageScale: number;
  setStageScale: (scale: number) => void;
}

const useSeatMapStore = create<SeatMapStore>((set, _get) => ({
  newItemView: null,
  setNewItemView: (newItemView: NewItem) => set({ newItemView }),
  editingItemId: null,
  setEditingItemId: (editingItemId: string | null) => set({ editingItemId }),
  stageDimensions: { w: 0, h: 0 },
  setStageDimensions: (stageDimensions: Dimensions) => set({ stageDimensions }),
  layoutDimensions: { w: LAYOUT_WIDTH, h: LAYOUT_HEIGHT, selected: false },
  setLayoutDimensions: (layoutDimensions: LayoutDimensions) => set({ layoutDimensions }),
  stageScale: 1,
  setStageScale: (stageScale: number) => set({ stageScale }),
}));

export default useSeatMapStore;
