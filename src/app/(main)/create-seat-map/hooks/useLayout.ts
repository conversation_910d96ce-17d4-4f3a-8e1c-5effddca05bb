import { useSeatMapContext } from "../context";
import { Position } from "@stores/useSeatMapStore";

export default function useLayout() {
  const { layerRef } = useSeatMapContext();

  const getInsertPosition = (): Position => {
    // Return center position for new sections
    // Since we removed the layout boundary, use a reasonable default
    return { x: 250, y: 250 };
  };

  return { getInsertPosition };
}
