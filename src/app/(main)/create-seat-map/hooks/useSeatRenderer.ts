import { useEffect, useRef, useCallback } from 'react';
import Konva from 'konva';
import { HighPerformanceSeatRenderer, SeatSection, Seat, Viewport } from '../lib/SeatRenderer';

export interface UseSeatRendererOptions {
  layer: Konva.Layer | null;
  stage: Konva.Stage | null;
}

export interface SeatRendererAPI {
  addSection: (section: SeatSection) => void;
  removeSection: (sectionId: string) => void;
  updateViewport: (viewport: Viewport) => void;
  getSelectedSeats: () => Seat[];
  clearSelection: () => void;
  selectSeat: (seatId: string) => void;
  deselectSeat: (seatId: string) => void;
  selectSeatsInRange: (startSeatId: string, endSeatId: string) => void;
  selectAllSeatsInSection: (sectionId: string) => void;
  selectAllActiveSeats: () => void;
  selectSeatsInArea: (x: number, y: number, width: number, height: number) => void;
  getSectionCount: () => number;
  getTotalSeatCount: () => number;
  getActiveSeatCount: () => number;
  toggleSeatActive: (seatId: string) => void;
  updateSeatLabel: (seatId: string, label: string) => void;
  renderer: HighPerformanceSeatRenderer | null;
}

export const useSeatRenderer = ({ layer, stage }: UseSeatRendererOptions): SeatRendererAPI => {
  const rendererRef = useRef<HighPerformanceSeatRenderer | null>(null);
  const sectionsRef = useRef<Map<string, SeatSection>>(new Map());
  const isInitializedRef = useRef(false);
  const apiRef = useRef<SeatRendererAPI | null>(null);

  // Initialize renderer when layer is available
  useEffect(() => {
    if (layer && !isInitializedRef.current) {
      rendererRef.current = new HighPerformanceSeatRenderer();
      rendererRef.current.initialize(layer);
      isInitializedRef.current = true;

      // Set up viewport tracking
      if (stage) {
        const updateViewportFromStage = () => {
          const viewport: Viewport = {
            x: -stage.x() / stage.scaleX(),
            y: -stage.y() / stage.scaleY(),
            width: stage.width() / stage.scaleX(),
            height: stage.height() / stage.scaleY(),
            scale: stage.scaleX(),
          };
          rendererRef.current?.updateViewport(viewport);
        };

        // Update viewport on stage changes
        stage.on('dragmove', updateViewportFromStage);
        stage.on('wheel', updateViewportFromStage);
        stage.on('scalechange', updateViewportFromStage);

        // Initial viewport update
        updateViewportFromStage();

        return () => {
          stage.off('dragmove', updateViewportFromStage);
          stage.off('wheel', updateViewportFromStage);
          stage.off('scalechange', updateViewportFromStage);
        };
      }
    }
  }, [layer, stage]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (rendererRef.current) {
        rendererRef.current.destroy();
        rendererRef.current = null;
      }
      sectionsRef.current.clear();
      isInitializedRef.current = false;
    };
  }, []);

  const addSection = useCallback((section: SeatSection) => {
    if (rendererRef.current) {
      sectionsRef.current.set(section.id, section);
      rendererRef.current.addSection(section);
    }
  }, []);

  const removeSection = useCallback((sectionId: string) => {
    if (rendererRef.current) {
      sectionsRef.current.delete(sectionId);
      rendererRef.current.removeSection(sectionId);
    }
  }, []);

  const updateViewport = useCallback((viewport: Viewport) => {
    if (rendererRef.current) {
      rendererRef.current.updateViewport(viewport);
    }
  }, []);

  const getSelectedSeats = useCallback((): Seat[] => {
    return rendererRef.current?.getSelectedSeats() || [];
  }, []);

  const clearSelection = useCallback(() => {
    if (rendererRef.current) {
      rendererRef.current.clearSelection();
    }
  }, []);

  const selectSeat = useCallback((seatId: string) => {
    if (rendererRef.current) {
      rendererRef.current.selectSeat(seatId);
    }
  }, []);

  const deselectSeat = useCallback((seatId: string) => {
    if (rendererRef.current) {
      rendererRef.current.deselectSeat(seatId);
    }
  }, []);

  const getSectionCount = useCallback((): number => {
    return sectionsRef.current.size;
  }, []);

  const getTotalSeatCount = useCallback((): number => {
    let total = 0;
    for (const section of sectionsRef.current.values()) {
      total += section.seats.length;
    }
    return total;
  }, []);

  const getActiveSeatCount = useCallback((): number => {
    let active = 0;
    for (const section of sectionsRef.current.values()) {
      active += section.seats.filter(seat => seat.isActive).length;
    }
    return active;
  }, []);

  const toggleSeatActive = useCallback((seatId: string) => {
    for (const section of sectionsRef.current.values()) {
      const seat = section.seats.find(s => s.id === seatId);
      if (seat) {
        seat.isActive = !seat.isActive;
        // Force re-render by updating the section
        if (rendererRef.current) {
          rendererRef.current.removeSection(section.id);
          rendererRef.current.addSection(section);
        }
        break;
      }
    }
  }, []);

  const updateSeatLabel = useCallback((seatId: string, label: string) => {
    for (const section of sectionsRef.current.values()) {
      const seat = section.seats.find(s => s.id === seatId);
      if (seat) {
        seat.label = label;
        // Force re-render by updating the section
        if (rendererRef.current) {
          rendererRef.current.removeSection(section.id);
          rendererRef.current.addSection(section);
        }
        break;
      }
    }
  }, []);

  const selectSeatsInRange = useCallback((startSeatId: string, endSeatId: string) => {
    if (rendererRef.current) {
      rendererRef.current.selectSeatsInRange(startSeatId, endSeatId);
    }
  }, []);

  const selectAllSeatsInSection = useCallback((sectionId: string) => {
    if (rendererRef.current) {
      rendererRef.current.selectAllSeatsInSection(sectionId);
    }
  }, []);

  const selectAllActiveSeats = useCallback(() => {
    if (rendererRef.current) {
      rendererRef.current.selectAllActiveSeats();
    }
  }, []);

  const selectSeatsInArea = useCallback((x: number, y: number, width: number, height: number) => {
    if (rendererRef.current) {
      rendererRef.current.selectSeatsInArea(x, y, width, height);
    }
  }, []);

  // Create stable API object
  if (!apiRef.current) {
    apiRef.current = {
      addSection,
      removeSection,
      updateViewport,
      getSelectedSeats,
      clearSelection,
      selectSeat,
      deselectSeat,
      selectSeatsInRange,
      selectAllSeatsInSection,
      selectAllActiveSeats,
      selectSeatsInArea,
      getSectionCount,
      getTotalSeatCount,
      getActiveSeatCount,
      toggleSeatActive,
      updateSeatLabel,
      renderer: rendererRef.current,
    };
  } else {
    // Update the renderer reference
    apiRef.current.renderer = rendererRef.current;
  }

  return apiRef.current!
};
