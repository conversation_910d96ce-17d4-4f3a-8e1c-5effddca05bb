import Konva from "konva";

export interface Seat {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  row: number;
  seatNumber: number;
  isActive: boolean;
  isSelected: boolean;
  isHovered: boolean;
  label?: string;
  color?: string;
  sectionId: string;
}

export interface SeatSection {
  id: string;
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  seats: Seat[];
  shape: 'rectangle' | 'arc' | 'polygon' | 'circle';
  shapeData?: any;
}

export interface Viewport {
  x: number;
  y: number;
  width: number;
  height: number;
  scale: number;
}

export class SpatialIndex {
  private gridSize: number;
  private grid: Map<string, Seat[]>;
  private bounds: { minX: number; minY: number; maxX: number; maxY: number };

  constructor(gridSize: number = 100) {
    this.gridSize = gridSize;
    this.grid = new Map();
    this.bounds = { minX: Infinity, minY: Infinity, maxX: -Infinity, maxY: -Infinity };
  }

  private getGridKey(x: number, y: number): string {
    const gridX = Math.floor(x / this.gridSize);
    const gridY = Math.floor(y / this.gridSize);
    return `${gridX},${gridY}`;
  }

  addSeat(seat: Seat): void {
    const key = this.getGridKey(seat.x, seat.y);
    if (!this.grid.has(key)) {
      this.grid.set(key, []);
    }
    this.grid.get(key)!.push(seat);

    // Update bounds
    this.bounds.minX = Math.min(this.bounds.minX, seat.x);
    this.bounds.minY = Math.min(this.bounds.minY, seat.y);
    this.bounds.maxX = Math.max(this.bounds.maxX, seat.x + seat.width);
    this.bounds.maxY = Math.max(this.bounds.maxY, seat.y + seat.height);
  }

  removeSeat(seat: Seat): void {
    const key = this.getGridKey(seat.x, seat.y);
    const seats = this.grid.get(key);
    if (seats) {
      const index = seats.findIndex(s => s.id === seat.id);
      if (index !== -1) {
        seats.splice(index, 1);
        if (seats.length === 0) {
          this.grid.delete(key);
        }
      }
    }
  }

  getSeatsInViewport(viewport: Viewport): Seat[] {
    const seats: Seat[] = [];
    const startGridX = Math.floor(viewport.x / this.gridSize);
    const endGridX = Math.floor((viewport.x + viewport.width) / this.gridSize);
    const startGridY = Math.floor(viewport.y / this.gridSize);
    const endGridY = Math.floor((viewport.y + viewport.height) / this.gridSize);

    for (let gridX = startGridX; gridX <= endGridX; gridX++) {
      for (let gridY = startGridY; gridY <= endGridY; gridY++) {
        const key = `${gridX},${gridY}`;
        const gridSeats = this.grid.get(key);
        if (gridSeats) {
          for (const seat of gridSeats) {
            // Check if seat intersects with viewport
            if (
              seat.x < viewport.x + viewport.width &&
              seat.x + seat.width > viewport.x &&
              seat.y < viewport.y + viewport.height &&
              seat.y + seat.height > viewport.y
            ) {
              seats.push(seat);
            }
          }
        }
      }
    }
    return seats;
  }

  getSeatAt(x: number, y: number): Seat | null {
    const key = this.getGridKey(x, y);
    const seats = this.grid.get(key);
    if (seats) {
      for (const seat of seats) {
        if (
          x >= seat.x &&
          x <= seat.x + seat.width &&
          y >= seat.y &&
          y <= seat.y + seat.height
        ) {
          return seat;
        }
      }
    }
    return null;
  }

  clear(): void {
    this.grid.clear();
    this.bounds = { minX: Infinity, minY: Infinity, maxX: -Infinity, maxY: -Infinity };
  }
}

export class HighPerformanceSeatRenderer {
  private spatialIndex: SpatialIndex;
  private sections: Map<string, SeatSection>;
  private sectionCanvases: Map<string, Konva.Group>;
  private layer: Konva.Layer | null;
  private dragLayer: Konva.Layer | null;
  private lastViewport: Viewport | null;
  private isDirty: boolean;
  private selectedSeats: Set<string>;
  private hoveredSeat: string | null;
  private renderCache: Map<string, HTMLCanvasElement>;

  constructor() {
    this.spatialIndex = new SpatialIndex();
    this.sections = new Map();
    this.sectionCanvases = new Map();
    this.layer = null;
    this.dragLayer = null;
    this.lastViewport = null;
    this.isDirty = true;
    this.selectedSeats = new Set();
    this.hoveredSeat = null;
    this.renderCache = new Map();
  }

  initialize(layer: Konva.Layer, dragLayer?: Konva.Layer): void {
    this.layer = layer;
    this.dragLayer = dragLayer || layer;
  }

  addSection(section: SeatSection): void {
    if (!this.layer) return;

    this.sections.set(section.id, section);

    // Create canvas for high-performance rendering
    const canvas = this.createSectionCanvas(section);

    // Create a draggable group with the canvas
    const sectionGroup = new Konva.Group({
      x: section.x,
      y: section.y,
      draggable: true,
      name: section.name,
      id: section.id,
    });

    // Add the pre-rendered canvas as an image
    const sectionImage = new Konva.Image({
      x: 0,
      y: 0,
      image: canvas,
      width: section.width,
      height: section.height,
    });

    // Add invisible hit area for interactions
    const hitArea = new Konva.Rect({
      x: 0,
      y: 0,
      width: section.width,
      height: section.height,
      fill: 'transparent',
    });

    sectionGroup.add(sectionImage);
    sectionGroup.add(hitArea);

    // Handle interactions
    this.setupSectionInteractions(sectionGroup, section);

    // Add to layer and store reference
    this.layer.add(sectionGroup);
    this.sectionCanvases.set(section.id, sectionGroup);

    // Add seats to spatial index
    for (const seat of section.seats) {
      this.spatialIndex.addSeat(seat);
    }

    this.layer.batchDraw();
  }

  private createSectionCanvas(section: SeatSection): HTMLCanvasElement {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;

    // Set canvas size with device pixel ratio for crisp rendering
    const dpr = window.devicePixelRatio || 1;
    canvas.width = section.width * dpr;
    canvas.height = section.height * dpr;
    canvas.style.width = section.width + 'px';
    canvas.style.height = section.height + 'px';
    ctx.scale(dpr, dpr);

    // Clear canvas
    ctx.clearRect(0, 0, section.width, section.height);

    // Batch render all seats
    this.renderSeatsToCanvas(ctx, section);

    return canvas;
  }

  private renderSeatsToCanvas(ctx: CanvasRenderingContext2D, section: SeatSection): void {
    const fontSize = Math.min(section.seats[0]?.width / 2 || 10, 12);

    ctx.font = `${fontSize}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    // Render seats in batches for better performance
    for (const seat of section.seats) {
      const x = seat.x - section.x;
      const y = seat.y - section.y;

      // Determine seat color based on state
      const isSelected = this.selectedSeats.has(seat.id);
      let fillColor = '#9CA3AF'; // inactive
      if (seat.isActive) {
        fillColor = isSelected ? '#EF4444' : '#3B82F6'; // selected red or active blue
      }

      // Draw seat rectangle
      ctx.fillStyle = fillColor;
      ctx.fillRect(x, y, seat.width, seat.height);

      // Draw seat border
      ctx.strokeStyle = '#1F2937';
      ctx.lineWidth = 1;
      ctx.strokeRect(x, y, seat.width, seat.height);

      // Draw seat label - always show for performance testing
      const labelText = seat.label || `${seat.row}-${seat.seatNumber}`;
      ctx.fillStyle = 'white';
      ctx.strokeStyle = 'black';
      ctx.lineWidth = 0.5;
      ctx.strokeText(labelText, x + seat.width / 2, y + seat.height / 2);
      ctx.fillText(labelText, x + seat.width / 2, y + seat.height / 2);
    }
  }

  private setupSectionInteractions(sectionGroup: Konva.Group, section: SeatSection): void {
    // Handle section dragging
    sectionGroup.on('dragstart', (e) => {
      if (this.dragLayer && this.dragLayer !== this.layer) {
        sectionGroup.moveTo(this.dragLayer);
      }
      e.cancelBubble = true;
    });

    sectionGroup.on('dragend', (e) => {
      if (this.layer && sectionGroup.getLayer() !== this.layer) {
        sectionGroup.moveTo(this.layer);
      }

      // Update section position
      section.x = sectionGroup.x();
      section.y = sectionGroup.y();

      // Update seat positions in the spatial index
      this.updateSectionInSpatialIndex(section);

      e.cancelBubble = true;
    });

    // Handle seat clicks by calculating which seat was clicked
    sectionGroup.on('click', (e) => {
      const pos = sectionGroup.getRelativePointerPosition();
      if (!pos) return;

      const clickedSeat = this.findSeatAtPosition(section, pos.x, pos.y);
      if (clickedSeat) {
        this.handleSeatClick(clickedSeat, e);
        // Re-render section to show selection changes
        this.updateSectionCanvas(section.id);
      }
    });
  }

  private findSeatAtPosition(section: SeatSection, x: number, y: number): Seat | null {
    for (const seat of section.seats) {
      const seatX = seat.x - section.x;
      const seatY = seat.y - section.y;

      if (x >= seatX && x <= seatX + seat.width &&
          y >= seatY && y <= seatY + seat.height) {
        return seat;
      }
    }
    return null;
  }

  private updateSectionCanvas(sectionId: string): void {
    const section = this.sections.get(sectionId);
    const sectionGroup = this.sectionCanvases.get(sectionId);

    if (!section || !sectionGroup) return;

    // Re-render the canvas
    const canvas = this.createSectionCanvas(section);
    const sectionImage = sectionGroup.findOne('Image') as Konva.Image;
    if (sectionImage) {
      sectionImage.image(canvas);
      this.layer?.batchDraw();
    }
  }

  removeSection(sectionId: string): void {
    const section = this.sections.get(sectionId);
    const sectionGroup = this.sectionCanvases.get(sectionId);

    if (section) {
      // Remove seats from spatial index
      for (const seat of section.seats) {
        this.spatialIndex.removeSeat(seat);
      }
      this.sections.delete(sectionId);
    }

    if (sectionGroup) {
      sectionGroup.destroy();
      this.sectionCanvases.delete(sectionId);
    }

    // Clean up render cache
    this.renderCache.delete(sectionId);

    this.layer?.batchDraw();
  }

  private updateSectionInSpatialIndex(section: SeatSection): void {
    // Remove old positions
    for (const seat of section.seats) {
      this.spatialIndex.removeSeat(seat);
    }

    // Update seat positions and re-add
    for (const seat of section.seats) {
      seat.x = section.x + (seat.x - section.x);
      seat.y = section.y + (seat.y - section.y);
      this.spatialIndex.addSeat(seat);
    }
  }

  private handleSeatClick(seat: Seat, e: Konva.KonvaEventObject<MouseEvent>): void {
    if (!seat.isActive) return;

    const isCtrlPressed = e.evt.ctrlKey || e.evt.metaKey;

    if (!isCtrlPressed) {
      // Single selection mode - clear previous selections
      this.clearSelection();
    }

    if (this.selectedSeats.has(seat.id)) {
      this.selectedSeats.delete(seat.id);
    } else {
      this.selectedSeats.add(seat.id);
    }

    this.updateSeatVisuals();
  }

  updateSeatVisuals(): void {
    // Re-render all sections to update visual state
    for (const sectionId of this.sections.keys()) {
      this.updateSectionCanvas(sectionId);
    }
  }

  updateSeatLabel(seatId: string, newLabel: string): void {
    // Find the section containing this seat
    for (const [sectionId, section] of this.sections) {
      const seat = section.seats.find(s => s.id === seatId);
      if (seat) {
        seat.label = newLabel;
        // Re-render the section canvas
        this.updateSectionCanvas(sectionId);
        break;
      }
    }
  }

  updateViewport(viewport: Viewport): void {
    this.lastViewport = { ...viewport };
    // Viewport changes don't affect individual Konva objects
  }



  getSelectedSeats(): Seat[] {
    const seats: Seat[] = [];
    for (const seatId of this.selectedSeats) {
      for (const section of this.sections.values()) {
        const seat = section.seats.find(s => s.id === seatId);
        if (seat) {
          seats.push(seat);
          break;
        }
      }
    }
    return seats;
  }

  clearSelection(): void {
    this.selectedSeats.clear();
    this.updateSeatVisuals();
  }

  selectSeat(seatId: string): void {
    this.selectedSeats.add(seatId);
    this.updateSeatVisuals();
  }

  deselectSeat(seatId: string): void {
    this.selectedSeats.delete(seatId);
    this.updateSeatVisuals();
  }

  selectSeatsInRange(startSeatId: string, endSeatId: string): void {
    // Find seats and select all seats between them (useful for shift+click)
    const allSeats: Seat[] = [];
    for (const section of this.sections.values()) {
      allSeats.push(...section.seats);
    }

    const startIndex = allSeats.findIndex(s => s.id === startSeatId);
    const endIndex = allSeats.findIndex(s => s.id === endSeatId);

    if (startIndex !== -1 && endIndex !== -1) {
      const minIndex = Math.min(startIndex, endIndex);
      const maxIndex = Math.max(startIndex, endIndex);

      for (let i = minIndex; i <= maxIndex; i++) {
        if (allSeats[i].isActive) {
          this.selectedSeats.add(allSeats[i].id);
        }
      }
      this.updateSeatVisuals();
    }
  }

  selectAllSeatsInSection(sectionId: string): void {
    const section = this.sections.get(sectionId);
    if (section) {
      section.seats.forEach(seat => {
        if (seat.isActive) {
          this.selectedSeats.add(seat.id);
        }
      });
      this.updateSeatVisuals();
    }
  }

  selectAllActiveSeats(): void {
    for (const section of this.sections.values()) {
      section.seats.forEach(seat => {
        if (seat.isActive) {
          this.selectedSeats.add(seat.id);
        }
      });
    }
    this.updateSeatVisuals();
  }

  getSeatsInArea(x: number, y: number, width: number, height: number): Seat[] {
    const viewport: Viewport = { x, y, width, height, scale: 1 };
    return this.spatialIndex.getSeatsInViewport(viewport);
  }

  selectSeatsInArea(x: number, y: number, width: number, height: number): void {
    const seatsInArea = this.getSeatsInArea(x, y, width, height);
    seatsInArea.forEach(seat => {
      if (seat.isActive) {
        this.selectedSeats.add(seat.id);
      }
    });
    this.updateSeatVisuals();
  }

  destroy(): void {
    // Destroy all section groups
    for (const sectionGroup of this.sectionCanvases.values()) {
      sectionGroup.destroy();
    }

    this.spatialIndex.clear();
    this.sections.clear();
    this.sectionCanvases.clear();
    this.selectedSeats.clear();
    this.renderCache.clear();
  }
}
