import Konva from "konva";

export interface Seat {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  row: number;
  seatNumber: number;
  isActive: boolean;
  isSelected: boolean;
  isHovered: boolean;
  label?: string;
  color?: string;
  sectionId: string;
}

export interface SeatSection {
  id: string;
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  seats: Seat[];
  shape: 'rectangle' | 'arc' | 'polygon' | 'circle';
  shapeData?: any;
}

export interface Viewport {
  x: number;
  y: number;
  width: number;
  height: number;
  scale: number;
}

export class SpatialIndex {
  private gridSize: number;
  private grid: Map<string, Seat[]>;
  private bounds: { minX: number; minY: number; maxX: number; maxY: number };

  constructor(gridSize: number = 100) {
    this.gridSize = gridSize;
    this.grid = new Map();
    this.bounds = { minX: Infinity, minY: Infinity, maxX: -Infinity, maxY: -Infinity };
  }

  private getGridKey(x: number, y: number): string {
    const gridX = Math.floor(x / this.gridSize);
    const gridY = Math.floor(y / this.gridSize);
    return `${gridX},${gridY}`;
  }

  addSeat(seat: Seat): void {
    const key = this.getGridKey(seat.x, seat.y);
    if (!this.grid.has(key)) {
      this.grid.set(key, []);
    }
    this.grid.get(key)!.push(seat);

    // Update bounds
    this.bounds.minX = Math.min(this.bounds.minX, seat.x);
    this.bounds.minY = Math.min(this.bounds.minY, seat.y);
    this.bounds.maxX = Math.max(this.bounds.maxX, seat.x + seat.width);
    this.bounds.maxY = Math.max(this.bounds.maxY, seat.y + seat.height);
  }

  removeSeat(seat: Seat): void {
    const key = this.getGridKey(seat.x, seat.y);
    const seats = this.grid.get(key);
    if (seats) {
      const index = seats.findIndex(s => s.id === seat.id);
      if (index !== -1) {
        seats.splice(index, 1);
        if (seats.length === 0) {
          this.grid.delete(key);
        }
      }
    }
  }

  getSeatsInViewport(viewport: Viewport): Seat[] {
    const seats: Seat[] = [];
    const startGridX = Math.floor(viewport.x / this.gridSize);
    const endGridX = Math.floor((viewport.x + viewport.width) / this.gridSize);
    const startGridY = Math.floor(viewport.y / this.gridSize);
    const endGridY = Math.floor((viewport.y + viewport.height) / this.gridSize);

    for (let gridX = startGridX; gridX <= endGridX; gridX++) {
      for (let gridY = startGridY; gridY <= endGridY; gridY++) {
        const key = `${gridX},${gridY}`;
        const gridSeats = this.grid.get(key);
        if (gridSeats) {
          for (const seat of gridSeats) {
            // Check if seat intersects with viewport
            if (
              seat.x < viewport.x + viewport.width &&
              seat.x + seat.width > viewport.x &&
              seat.y < viewport.y + viewport.height &&
              seat.y + seat.height > viewport.y
            ) {
              seats.push(seat);
            }
          }
        }
      }
    }
    return seats;
  }

  getSeatAt(x: number, y: number): Seat | null {
    const key = this.getGridKey(x, y);
    const seats = this.grid.get(key);
    if (seats) {
      for (const seat of seats) {
        if (
          x >= seat.x &&
          x <= seat.x + seat.width &&
          y >= seat.y &&
          y <= seat.y + seat.height
        ) {
          return seat;
        }
      }
    }
    return null;
  }

  clear(): void {
    this.grid.clear();
    this.bounds = { minX: Infinity, minY: Infinity, maxX: -Infinity, maxY: -Infinity };
  }
}

export class HighPerformanceSeatRenderer {
  private spatialIndex: SpatialIndex;
  private sections: Map<string, SeatSection>;
  private sectionGroups: Map<string, Konva.Group>;
  private layer: Konva.Layer | null;
  private dragLayer: Konva.Layer | null;
  private lastViewport: Viewport | null;
  private isDirty: boolean;
  private selectedSeats: Set<string>;
  private hoveredSeat: string | null;

  constructor() {
    this.spatialIndex = new SpatialIndex();
    this.sections = new Map();
    this.sectionGroups = new Map();
    this.layer = null;
    this.dragLayer = null;
    this.lastViewport = null;
    this.isDirty = true;
    this.selectedSeats = new Set();
    this.hoveredSeat = null;
  }

  initialize(layer: Konva.Layer, dragLayer?: Konva.Layer): void {
    this.layer = layer;
    this.dragLayer = dragLayer || layer;
  }

  addSection(section: SeatSection): void {
    console.log('addSection called with:', section.id, 'seats:', section.seats.length);
    if (!this.layer) {
      console.error('No layer available for rendering');
      return;
    }

    this.sections.set(section.id, section);

    // Create a draggable group for this section
    const sectionGroup = new Konva.Group({
      x: section.x,
      y: section.y,
      draggable: true,
      name: section.name,
      id: section.id,
    });

    // Add section background (optional, for visual feedback)
    const sectionBg = new Konva.Rect({
      x: 0,
      y: 0,
      width: section.width,
      height: section.height,
      fill: 'rgba(200, 200, 200, 0.1)',
      stroke: '#ccc',
      strokeWidth: 1,
      listening: false,
    });
    sectionGroup.add(sectionBg);

    // Create seats as individual Konva objects within the group
    section.seats.forEach((seat) => {
      const seatRect = new Konva.Rect({
        x: seat.x - section.x, // Relative to section
        y: seat.y - section.y, // Relative to section
        width: seat.width,
        height: seat.height,
        fill: seat.isActive ? '#3B82F6' : '#9CA3AF',
        stroke: '#1F2937',
        strokeWidth: 1,
        id: seat.id,
        name: 'seat',
      });

      // Add seat label - always show labels for seats
      const labelText = seat.label || `${seat.row}-${seat.seatNumber}`;
      console.log(`Creating label for seat ${seat.id}: "${labelText}" at position (${seat.x - section.x}, ${seat.y - section.y})`);

      const seatLabel = new Konva.Text({
        x: seat.x - section.x,
        y: seat.y - section.y,
        width: seat.width,
        height: seat.height,
        text: labelText,
        fontSize: Math.max(10, Math.min(seat.width / 2, 14)),
        fontFamily: 'Arial',
        fill: 'black',
        stroke: 'white',
        strokeWidth: 0.5,
        align: 'center',
        verticalAlign: 'middle',
        listening: false,
        perfectDrawEnabled: false,
        id: `${seat.id}-label`,
        name: 'seat-label',
      });
      sectionGroup.add(seatLabel);

      // Handle seat clicks
      seatRect.on('click', (e) => {
        e.cancelBubble = true;
        this.handleSeatClick(seat, e);
      });

      seatRect.on('mouseenter', () => {
        this.hoveredSeat = seat.id;
        seatRect.fill(seat.isActive ? '#10B981' : '#9CA3AF');
        this.layer?.batchDraw();
      });

      seatRect.on('mouseleave', () => {
        this.hoveredSeat = null;
        const isSelected = this.selectedSeats.has(seat.id);
        seatRect.fill(
          !seat.isActive ? '#9CA3AF' :
          isSelected ? '#EF4444' : '#3B82F6'
        );
        this.layer?.batchDraw();
      });

      sectionGroup.add(seatRect);
    });

    // Handle section dragging
    sectionGroup.on('dragstart', (e) => {
      if (this.dragLayer && this.dragLayer !== this.layer) {
        sectionGroup.moveTo(this.dragLayer);
      }
      e.cancelBubble = true;
    });

    sectionGroup.on('dragend', (e) => {
      if (this.layer && sectionGroup.getLayer() !== this.layer) {
        sectionGroup.moveTo(this.layer);
      }

      // Update section position
      section.x = sectionGroup.x();
      section.y = sectionGroup.y();

      // Update seat positions in the spatial index
      this.updateSectionInSpatialIndex(section);

      e.cancelBubble = true;
    });

    // Add to layer and store reference
    this.layer.add(sectionGroup);
    this.sectionGroups.set(section.id, sectionGroup);

    // Add seats to spatial index
    for (const seat of section.seats) {
      this.spatialIndex.addSeat(seat);
    }

    this.layer.batchDraw();
  }

  removeSection(sectionId: string): void {
    const section = this.sections.get(sectionId);
    const sectionGroup = this.sectionGroups.get(sectionId);

    if (section) {
      // Remove seats from spatial index
      for (const seat of section.seats) {
        this.spatialIndex.removeSeat(seat);
      }
      this.sections.delete(sectionId);
    }

    if (sectionGroup) {
      sectionGroup.destroy();
      this.sectionGroups.delete(sectionId);
    }

    this.layer?.batchDraw();
  }

  private updateSectionInSpatialIndex(section: SeatSection): void {
    // Remove old positions
    for (const seat of section.seats) {
      this.spatialIndex.removeSeat(seat);
    }

    // Update seat positions and re-add
    for (const seat of section.seats) {
      seat.x = section.x + (seat.x - section.x);
      seat.y = section.y + (seat.y - section.y);
      this.spatialIndex.addSeat(seat);
    }
  }

  private handleSeatClick(seat: Seat, e: Konva.KonvaEventObject<MouseEvent>): void {
    if (!seat.isActive) return;

    const isCtrlPressed = e.evt.ctrlKey || e.evt.metaKey;

    if (!isCtrlPressed) {
      // Single selection mode - clear previous selections
      this.clearSelection();
    }

    if (this.selectedSeats.has(seat.id)) {
      this.selectedSeats.delete(seat.id);
    } else {
      this.selectedSeats.add(seat.id);
    }

    this.updateSeatVisuals();
  }

  updateSeatVisuals(): void {
    for (const [sectionId, sectionGroup] of this.sectionGroups) {
      const section = this.sections.get(sectionId);
      if (!section) continue;

      section.seats.forEach((seat) => {
        const seatRect = sectionGroup.findOne(`#${seat.id}`) as Konva.Rect;
        if (seatRect) {
          const isSelected = this.selectedSeats.has(seat.id);
          const isHovered = this.hoveredSeat === seat.id;

          let fillColor = seat.isActive ? '#3B82F6' : '#9CA3AF';
          if (isSelected) fillColor = '#EF4444';
          else if (isHovered) fillColor = '#10B981';

          seatRect.fill(fillColor);
        }
      });
    }

    this.layer?.batchDraw();
  }

  updateSeatLabel(seatId: string, newLabel: string): void {
    for (const [sectionId, sectionGroup] of this.sectionGroups) {
      const section = this.sections.get(sectionId);
      if (!section) continue;

      const seat = section.seats.find(s => s.id === seatId);
      if (seat) {
        // Find and update the text label
        const labelNode = sectionGroup.findOne(`#${seatId}-label`) as Konva.Text;
        if (labelNode) {
          labelNode.text(newLabel);
          this.layer?.batchDraw();
        }
        break;
      }
    }
  }

  updateViewport(viewport: Viewport): void {
    this.lastViewport = { ...viewport };
    // Viewport changes don't affect individual Konva objects
  }



  getSelectedSeats(): Seat[] {
    const seats: Seat[] = [];
    for (const seatId of this.selectedSeats) {
      for (const section of this.sections.values()) {
        const seat = section.seats.find(s => s.id === seatId);
        if (seat) {
          seats.push(seat);
          break;
        }
      }
    }
    return seats;
  }

  clearSelection(): void {
    this.selectedSeats.clear();
    this.updateSeatVisuals();
  }

  selectSeat(seatId: string): void {
    this.selectedSeats.add(seatId);
    this.updateSeatVisuals();
  }

  deselectSeat(seatId: string): void {
    this.selectedSeats.delete(seatId);
    this.updateSeatVisuals();
  }

  selectSeatsInRange(startSeatId: string, endSeatId: string): void {
    // Find seats and select all seats between them (useful for shift+click)
    const allSeats: Seat[] = [];
    for (const section of this.sections.values()) {
      allSeats.push(...section.seats);
    }

    const startIndex = allSeats.findIndex(s => s.id === startSeatId);
    const endIndex = allSeats.findIndex(s => s.id === endSeatId);

    if (startIndex !== -1 && endIndex !== -1) {
      const minIndex = Math.min(startIndex, endIndex);
      const maxIndex = Math.max(startIndex, endIndex);

      for (let i = minIndex; i <= maxIndex; i++) {
        if (allSeats[i].isActive) {
          this.selectedSeats.add(allSeats[i].id);
        }
      }
      this.updateSeatVisuals();
    }
  }

  selectAllSeatsInSection(sectionId: string): void {
    const section = this.sections.get(sectionId);
    if (section) {
      section.seats.forEach(seat => {
        if (seat.isActive) {
          this.selectedSeats.add(seat.id);
        }
      });
      this.updateSeatVisuals();
    }
  }

  selectAllActiveSeats(): void {
    for (const section of this.sections.values()) {
      section.seats.forEach(seat => {
        if (seat.isActive) {
          this.selectedSeats.add(seat.id);
        }
      });
    }
    this.updateSeatVisuals();
  }

  getSeatsInArea(x: number, y: number, width: number, height: number): Seat[] {
    const viewport: Viewport = { x, y, width, height, scale: 1 };
    return this.spatialIndex.getSeatsInViewport(viewport);
  }

  selectSeatsInArea(x: number, y: number, width: number, height: number): void {
    const seatsInArea = this.getSeatsInArea(x, y, width, height);
    seatsInArea.forEach(seat => {
      if (seat.isActive) {
        this.selectedSeats.add(seat.id);
      }
    });
    this.updateSeatVisuals();
  }

  destroy(): void {
    // Destroy all section groups
    for (const sectionGroup of this.sectionGroups.values()) {
      sectionGroup.destroy();
    }

    this.spatialIndex.clear();
    this.sections.clear();
    this.sectionGroups.clear();
    this.selectedSeats.clear();
  }
}
