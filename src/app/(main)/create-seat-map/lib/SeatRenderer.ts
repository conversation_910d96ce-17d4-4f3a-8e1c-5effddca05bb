import Konva from "konva";

export interface Seat {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  row: number;
  seatNumber: number;
  isActive: boolean;
  isSelected: boolean;
  isHovered: boolean;
  label?: string;
  color?: string;
  sectionId: string;
}

export interface SeatSection {
  id: string;
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  seats: Seat[];
  shape: 'rectangle' | 'arc' | 'polygon' | 'circle';
  shapeData?: any;
}

export interface Viewport {
  x: number;
  y: number;
  width: number;
  height: number;
  scale: number;
}

export class SpatialIndex {
  private gridSize: number;
  private grid: Map<string, Seat[]>;
  private bounds: { minX: number; minY: number; maxX: number; maxY: number };

  constructor(gridSize: number = 100) {
    this.gridSize = gridSize;
    this.grid = new Map();
    this.bounds = { minX: Infinity, minY: Infinity, maxX: -Infinity, maxY: -Infinity };
  }

  private getGridKey(x: number, y: number): string {
    const gridX = Math.floor(x / this.gridSize);
    const gridY = Math.floor(y / this.gridSize);
    return `${gridX},${gridY}`;
  }

  addSeat(seat: Seat): void {
    const key = this.getGridKey(seat.x, seat.y);
    if (!this.grid.has(key)) {
      this.grid.set(key, []);
    }
    this.grid.get(key)!.push(seat);

    // Update bounds
    this.bounds.minX = Math.min(this.bounds.minX, seat.x);
    this.bounds.minY = Math.min(this.bounds.minY, seat.y);
    this.bounds.maxX = Math.max(this.bounds.maxX, seat.x + seat.width);
    this.bounds.maxY = Math.max(this.bounds.maxY, seat.y + seat.height);
  }

  removeSeat(seat: Seat): void {
    const key = this.getGridKey(seat.x, seat.y);
    const seats = this.grid.get(key);
    if (seats) {
      const index = seats.findIndex(s => s.id === seat.id);
      if (index !== -1) {
        seats.splice(index, 1);
        if (seats.length === 0) {
          this.grid.delete(key);
        }
      }
    }
  }

  getSeatsInViewport(viewport: Viewport): Seat[] {
    const seats: Seat[] = [];
    const startGridX = Math.floor(viewport.x / this.gridSize);
    const endGridX = Math.floor((viewport.x + viewport.width) / this.gridSize);
    const startGridY = Math.floor(viewport.y / this.gridSize);
    const endGridY = Math.floor((viewport.y + viewport.height) / this.gridSize);

    for (let gridX = startGridX; gridX <= endGridX; gridX++) {
      for (let gridY = startGridY; gridY <= endGridY; gridY++) {
        const key = `${gridX},${gridY}`;
        const gridSeats = this.grid.get(key);
        if (gridSeats) {
          for (const seat of gridSeats) {
            // Check if seat intersects with viewport
            if (
              seat.x < viewport.x + viewport.width &&
              seat.x + seat.width > viewport.x &&
              seat.y < viewport.y + viewport.height &&
              seat.y + seat.height > viewport.y
            ) {
              seats.push(seat);
            }
          }
        }
      }
    }
    return seats;
  }

  getSeatAt(x: number, y: number): Seat | null {
    const key = this.getGridKey(x, y);
    const seats = this.grid.get(key);
    if (seats) {
      for (const seat of seats) {
        if (
          x >= seat.x &&
          x <= seat.x + seat.width &&
          y >= seat.y &&
          y <= seat.y + seat.height
        ) {
          return seat;
        }
      }
    }
    return null;
  }

  clear(): void {
    this.grid.clear();
    this.bounds = { minX: Infinity, minY: Infinity, maxX: -Infinity, maxY: -Infinity };
  }
}

export class HighPerformanceSeatRenderer {
  private spatialIndex: SpatialIndex;
  private sections: Map<string, SeatSection>;
  private konvaShape: Konva.Shape | null;
  private layer: Konva.Layer | null;
  private lastViewport: Viewport | null;
  private isDirty: boolean;
  private selectedSeats: Set<string>;
  private hoveredSeat: string | null;

  constructor() {
    this.spatialIndex = new SpatialIndex();
    this.sections = new Map();
    this.konvaShape = null;
    this.layer = null;
    this.lastViewport = null;
    this.isDirty = true;
    this.selectedSeats = new Set();
    this.hoveredSeat = null;
  }

  initialize(layer: Konva.Layer): void {
    this.layer = layer;
    this.konvaShape = new Konva.Shape({
      sceneFunc: (context, shape) => this.drawSeats(context, shape),
      hitFunc: (context, shape) => this.drawHitArea(context, shape),
    });

    this.konvaShape.on('click', (e) => this.handleClick(e));
    this.konvaShape.on('mousemove', (e) => this.handleMouseMove(e));
    this.konvaShape.on('mouseleave', () => this.handleMouseLeave());

    layer.add(this.konvaShape);
  }

  addSection(section: SeatSection): void {
    this.sections.set(section.id, section);
    
    // Add all seats to spatial index
    for (const seat of section.seats) {
      this.spatialIndex.addSeat(seat);
    }
    
    this.markDirty();
  }

  removeSection(sectionId: string): void {
    const section = this.sections.get(sectionId);
    if (section) {
      // Remove seats from spatial index
      for (const seat of section.seats) {
        this.spatialIndex.removeSeat(seat);
      }
      this.sections.delete(sectionId);
      this.markDirty();
    }
  }

  updateViewport(viewport: Viewport): void {
    if (!this.lastViewport || this.viewportChanged(viewport)) {
      this.lastViewport = { ...viewport };
      this.markDirty();
    }
  }

  private viewportChanged(viewport: Viewport): boolean {
    if (!this.lastViewport) return true;
    
    const threshold = 10; // pixels
    return (
      Math.abs(viewport.x - this.lastViewport.x) > threshold ||
      Math.abs(viewport.y - this.lastViewport.y) > threshold ||
      Math.abs(viewport.scale - this.lastViewport.scale) > 0.1
    );
  }

  private markDirty(): void {
    this.isDirty = true;
    if (this.konvaShape) {
      this.konvaShape.getLayer()?.batchDraw();
    }
  }

  private drawSeats(context: CanvasRenderingContext2D, shape: Konva.Shape): void {
    if (!this.lastViewport) return;

    const visibleSeats = this.spatialIndex.getSeatsInViewport(this.lastViewport);
    
    // Only redraw if viewport changed or seats are dirty
    if (!this.isDirty && visibleSeats.length === 0) return;

    context.save();
    
    for (const seat of visibleSeats) {
      this.drawSeat(context, seat);
    }
    
    context.restore();
    this.isDirty = false;
  }

  private drawSeat(context: CanvasRenderingContext2D, seat: Seat): void {
    const isSelected = this.selectedSeats.has(seat.id);
    const isHovered = this.hoveredSeat === seat.id;
    
    // Determine seat color
    let fillColor = seat.color || '#3B82F6'; // Default blue
    if (!seat.isActive) {
      fillColor = '#9CA3AF'; // Gray for inactive
    } else if (isSelected) {
      fillColor = '#EF4444'; // Red for selected
    } else if (isHovered) {
      fillColor = '#10B981'; // Green for hovered
    }

    // Draw seat rectangle
    context.fillStyle = fillColor;
    context.fillRect(seat.x, seat.y, seat.width, seat.height);

    // Draw seat border
    context.strokeStyle = '#1F2937';
    context.lineWidth = 1;
    context.strokeRect(seat.x, seat.y, seat.width, seat.height);

    // Draw seat label if visible (only when zoomed in enough)
    if (this.lastViewport && this.lastViewport.scale > 0.5 && seat.label) {
      context.fillStyle = '#FFFFFF';
      context.font = `${Math.min(seat.width / 3, 12)}px Arial`;
      context.textAlign = 'center';
      context.textBaseline = 'middle';
      context.fillText(
        seat.label,
        seat.x + seat.width / 2,
        seat.y + seat.height / 2
      );
    }
  }

  private drawHitArea(context: CanvasRenderingContext2D, shape: Konva.Shape): void {
    if (!this.lastViewport) return;

    const visibleSeats = this.spatialIndex.getSeatsInViewport(this.lastViewport);
    
    for (const seat of visibleSeats) {
      context.fillRect(seat.x, seat.y, seat.width, seat.height);
    }
  }

  private handleClick(e: Konva.KonvaEventObject<MouseEvent>): void {
    const pos = e.target.getStage()?.getPointerPosition();
    if (!pos) return;

    const seat = this.spatialIndex.getSeatAt(pos.x, pos.y);
    if (seat && seat.isActive) {
      if (this.selectedSeats.has(seat.id)) {
        this.selectedSeats.delete(seat.id);
      } else {
        this.selectedSeats.add(seat.id);
      }
      this.markDirty();
    }
  }

  private handleMouseMove(e: Konva.KonvaEventObject<MouseEvent>): void {
    const pos = e.target.getStage()?.getPointerPosition();
    if (!pos) return;

    const seat = this.spatialIndex.getSeatAt(pos.x, pos.y);
    const newHoveredSeat = seat?.id || null;
    
    if (newHoveredSeat !== this.hoveredSeat) {
      this.hoveredSeat = newHoveredSeat;
      this.markDirty();
    }
  }

  private handleMouseLeave(): void {
    if (this.hoveredSeat) {
      this.hoveredSeat = null;
      this.markDirty();
    }
  }

  getSelectedSeats(): Seat[] {
    const seats: Seat[] = [];
    for (const seatId of this.selectedSeats) {
      for (const section of this.sections.values()) {
        const seat = section.seats.find(s => s.id === seatId);
        if (seat) {
          seats.push(seat);
          break;
        }
      }
    }
    return seats;
  }

  clearSelection(): void {
    this.selectedSeats.clear();
    this.markDirty();
  }

  selectSeat(seatId: string): void {
    this.selectedSeats.add(seatId);
    this.markDirty();
  }

  deselectSeat(seatId: string): void {
    this.selectedSeats.delete(seatId);
    this.markDirty();
  }

  destroy(): void {
    if (this.konvaShape) {
      this.konvaShape.destroy();
    }
    this.spatialIndex.clear();
    this.sections.clear();
    this.selectedSeats.clear();
  }
}
