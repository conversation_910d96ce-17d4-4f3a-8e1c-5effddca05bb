import { Seat, SeatSection } from './SeatRenderer';

export interface ShapeConfig {
  seatWidth: number;
  seatHeight: number;
  seatSpacing: number;
  rowSpacing: number;
  padding: number;
}

export interface RectangleConfig extends ShapeConfig {
  rows: number;
  seatsPerRow: number;
}

export interface ArcConfig extends ShapeConfig {
  radius: number;
  startAngle: number;
  endAngle: number;
  rows: number;
}

export interface PolygonConfig extends ShapeConfig {
  points: number[];
  capacity: number;
}

export interface CircleConfig extends ShapeConfig {
  radius: number;
  capacity: number;
}

export class ShapeSeatGenerator {
  static generateRectangleSeats(
    sectionId: string,
    x: number,
    y: number,
    config: RectangleConfig
  ): SeatSection {
    const seats: Seat[] = [];
    let seatId = 1;

    for (let row = 0; row < config.rows; row++) {
      for (let seatInRow = 0; seatInRow < config.seatsPerRow; seatInRow++) {
        const seatX = x + config.padding + seatInRow * (config.seatWidth + config.seatSpacing);
        const seatY = y + config.padding + row * (config.seatHeight + config.rowSpacing);

        seats.push({
          id: `${sectionId}-seat-${row + 1}-${seatInRow + 1}`,
          x: seatX,
          y: seatY,
          width: config.seatWidth,
          height: config.seatHeight,
          row: row + 1,
          seatNumber: seatInRow + 1,
          isActive: true,
          isSelected: false,
          isHovered: false,
          label: `${row + 1}-${seatInRow + 1}`,
          sectionId,
        });
        seatId++;
      }
    }

    const sectionWidth = config.seatsPerRow * config.seatWidth + 
                        (config.seatsPerRow - 1) * config.seatSpacing + 
                        config.padding * 2;
    const sectionHeight = config.rows * config.seatHeight + 
                         (config.rows - 1) * config.rowSpacing + 
                         config.padding * 2;

    return {
      id: sectionId,
      name: `Section ${sectionId}`,
      x,
      y,
      width: sectionWidth,
      height: sectionHeight,
      seats,
      shape: 'rectangle',
    };
  }

  static generateArcSeats(
    sectionId: string,
    centerX: number,
    centerY: number,
    config: ArcConfig
  ): SeatSection {
    const seats: Seat[] = [];
    const angleRange = config.endAngle - config.startAngle;
    
    for (let row = 0; row < config.rows; row++) {
      const currentRadius = config.radius + row * (config.seatHeight + config.rowSpacing);
      const circumference = 2 * Math.PI * currentRadius;
      const arcLength = (angleRange / (2 * Math.PI)) * circumference;
      const seatsInRow = Math.floor(arcLength / (config.seatWidth + config.seatSpacing));
      
      for (let seatInRow = 0; seatInRow < seatsInRow; seatInRow++) {
        const angleStep = angleRange / seatsInRow;
        const angle = config.startAngle + seatInRow * angleStep;
        
        const seatX = centerX + Math.cos(angle) * currentRadius - config.seatWidth / 2;
        const seatY = centerY + Math.sin(angle) * currentRadius - config.seatHeight / 2;

        seats.push({
          id: `${sectionId}-seat-${row + 1}-${seatInRow + 1}`,
          x: seatX,
          y: seatY,
          width: config.seatWidth,
          height: config.seatHeight,
          row: row + 1,
          seatNumber: seatInRow + 1,
          isActive: true,
          isSelected: false,
          isHovered: false,
          label: `${row + 1}-${seatInRow + 1}`,
          sectionId,
        });
      }
    }

    const bounds = this.calculateBounds(seats);
    
    return {
      id: sectionId,
      name: `Arc Section ${sectionId}`,
      x: bounds.minX,
      y: bounds.minY,
      width: bounds.maxX - bounds.minX,
      height: bounds.maxY - bounds.minY,
      seats,
      shape: 'arc',
      shapeData: config,
    };
  }

  static generatePolygonSeats(
    sectionId: string,
    config: PolygonConfig
  ): SeatSection {
    const seats: Seat[] = [];
    const bounds = this.getPolygonBounds(config.points);
    
    // Calculate grid dimensions based on capacity
    const area = this.calculatePolygonArea(config.points);
    const seatArea = config.seatWidth * config.seatHeight;
    const totalSeatArea = config.capacity * seatArea;
    const efficiency = 0.7; // Account for spacing and irregular shape
    
    if (totalSeatArea > area * efficiency) {
      console.warn('Capacity too high for polygon area');
    }

    // Generate grid of potential seat positions
    const cols = Math.floor(bounds.width / (config.seatWidth + config.seatSpacing));
    const rows = Math.floor(bounds.height / (config.seatHeight + config.rowSpacing));
    
    let seatsPlaced = 0;
    let seatId = 1;

    for (let row = 0; row < rows && seatsPlaced < config.capacity; row++) {
      for (let col = 0; col < cols && seatsPlaced < config.capacity; col++) {
        const seatX = bounds.minX + col * (config.seatWidth + config.seatSpacing);
        const seatY = bounds.minY + row * (config.seatHeight + config.rowSpacing);
        
        // Check if seat center is inside polygon
        const seatCenterX = seatX + config.seatWidth / 2;
        const seatCenterY = seatY + config.seatHeight / 2;
        
        if (this.isPointInPolygon(seatCenterX, seatCenterY, config.points)) {
          seats.push({
            id: `${sectionId}-seat-${seatId}`,
            x: seatX,
            y: seatY,
            width: config.seatWidth,
            height: config.seatHeight,
            row: row + 1,
            seatNumber: (seatsPlaced % cols) + 1,
            isActive: true,
            isSelected: false,
            isHovered: false,
            label: `${row + 1}-${(seatsPlaced % cols) + 1}`,
            sectionId,
          });
          seatsPlaced++;
          seatId++;
        }
      }
    }

    return {
      id: sectionId,
      name: `Polygon Section ${sectionId}`,
      x: bounds.minX,
      y: bounds.minY,
      width: bounds.width,
      height: bounds.height,
      seats,
      shape: 'polygon',
      shapeData: config,
    };
  }

  static generateCircleSeats(
    sectionId: string,
    centerX: number,
    centerY: number,
    config: CircleConfig
  ): SeatSection {
    const seats: Seat[] = [];
    const area = Math.PI * config.radius * config.radius;
    const seatArea = config.seatWidth * config.seatHeight;
    const efficiency = 0.6; // Account for circular packing efficiency
    
    // Calculate concentric rings
    const rings = Math.floor(config.radius / (config.seatHeight + config.rowSpacing));
    let seatsPlaced = 0;
    let seatId = 1;

    for (let ring = 0; ring < rings && seatsPlaced < config.capacity; ring++) {
      const ringRadius = (ring + 1) * (config.seatHeight + config.rowSpacing);
      const circumference = 2 * Math.PI * ringRadius;
      const seatsInRing = Math.min(
        Math.floor(circumference / (config.seatWidth + config.seatSpacing)),
        config.capacity - seatsPlaced
      );
      
      for (let seatInRing = 0; seatInRing < seatsInRing; seatInRing++) {
        const angle = (2 * Math.PI * seatInRing) / seatsInRing;
        const seatX = centerX + Math.cos(angle) * ringRadius - config.seatWidth / 2;
        const seatY = centerY + Math.sin(angle) * ringRadius - config.seatHeight / 2;

        seats.push({
          id: `${sectionId}-seat-${ring + 1}-${seatInRing + 1}`,
          x: seatX,
          y: seatY,
          width: config.seatWidth,
          height: config.seatHeight,
          row: ring + 1,
          seatNumber: seatInRing + 1,
          isActive: true,
          isSelected: false,
          isHovered: false,
          label: `${ring + 1}-${seatInRing + 1}`,
          sectionId,
        });
        seatsPlaced++;
        seatId++;
      }
    }

    return {
      id: sectionId,
      name: `Circle Section ${sectionId}`,
      x: centerX - config.radius,
      y: centerY - config.radius,
      width: config.radius * 2,
      height: config.radius * 2,
      seats,
      shape: 'circle',
      shapeData: config,
    };
  }

  private static calculateBounds(seats: Seat[]): { minX: number; minY: number; maxX: number; maxY: number } {
    if (seats.length === 0) {
      return { minX: 0, minY: 0, maxX: 0, maxY: 0 };
    }

    let minX = seats[0].x;
    let minY = seats[0].y;
    let maxX = seats[0].x + seats[0].width;
    let maxY = seats[0].y + seats[0].height;

    for (const seat of seats) {
      minX = Math.min(minX, seat.x);
      minY = Math.min(minY, seat.y);
      maxX = Math.max(maxX, seat.x + seat.width);
      maxY = Math.max(maxY, seat.y + seat.height);
    }

    return { minX, minY, maxX, maxY };
  }

  private static getPolygonBounds(points: number[]): { minX: number; minY: number; maxX: number; maxY: number; width: number; height: number } {
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    for (let i = 0; i < points.length; i += 2) {
      const x = points[i];
      const y = points[i + 1];
      minX = Math.min(minX, x);
      maxX = Math.max(maxX, x);
      minY = Math.min(minY, y);
      maxY = Math.max(maxY, y);
    }

    return {
      minX,
      minY,
      maxX,
      maxY,
      width: maxX - minX,
      height: maxY - minY,
    };
  }

  private static calculatePolygonArea(points: number[]): number {
    let area = 0;
    const n = points.length / 2;
    
    for (let i = 0; i < n; i++) {
      const j = (i + 1) % n;
      const xi = points[i * 2];
      const yi = points[i * 2 + 1];
      const xj = points[j * 2];
      const yj = points[j * 2 + 1];
      area += xi * yj - xj * yi;
    }
    
    return Math.abs(area) / 2;
  }

  private static isPointInPolygon(x: number, y: number, points: number[]): boolean {
    let inside = false;
    const n = points.length / 2;
    
    for (let i = 0, j = n - 1; i < n; j = i++) {
      const xi = points[i * 2];
      const yi = points[i * 2 + 1];
      const xj = points[j * 2];
      const yj = points[j * 2 + 1];
      
      if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
        inside = !inside;
      }
    }
    
    return inside;
  }
}
