import Disclaimer from "@components/primitive/Disclaimer";
import { CustomNumberInput } from "@components/primitive/CustomNumberInput";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@components/shadcn/Form";
import Name from "@components/form/Name";
import Button from "@components/primitive/Button";
import { useSeatMapContext } from "../context";
import useLayout from "../hooks/useLayout";
import { SEAT_SIZE, SEAT_GAP, SHAPE_PADDING } from "../lib/config";
import { ShapeSeatGenerator } from "../lib/ShapeSeats";
import { useSeatRenderer } from "../hooks/useSeatRenderer";

const FormSchema = z.object({
  name: z.string().min(1, { message: "Name is required" }),
  radius: z
    .number()
    .min(50, { message: "Minimum radius is 50px" })
    .max(1000, { message: "Maximum radius is 1000px" }),
  startAngle: z
    .number()
    .min(0, { message: "Start angle must be >= 0" })
    .max(360, { message: "Start angle must be <= 360" }),
  endAngle: z
    .number()
    .min(0, { message: "End angle must be >= 0" })
    .max(360, { message: "End angle must be <= 360" }),
  rows: z
    .number()
    .min(1, { message: "At least 1 row required" })
    .max(50, { message: "Max. 50 rows allowed" }),
});

export default function AddArcSection() {
  const { layerRef } = useSeatMapContext();
  const { getInsertPosition } = useLayout();
  const seatRenderer = useSeatRenderer({
    layer: layerRef.current,
    stage: null,
  });

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: { 
      name: "", 
      radius: 200, 
      startAngle: 0, 
      endAngle: 180, 
      rows: 5 
    },
  });

  const formErrors = form.formState.errors;
  const nameError = formErrors.name?.message;

  const onSubmit = async () => {
    try {
      const { x, y } = getInsertPosition();
      const name = form.getValues("name");
      const radius = form.getValues("radius");
      const startAngle = form.getValues("startAngle");
      const endAngle = form.getValues("endAngle");
      const rows = form.getValues("rows");

      // Validate angle range
      if (endAngle <= startAngle) {
        form.setError("endAngle", { message: "End angle must be greater than start angle" });
        return;
      }

      // Convert degrees to radians
      const startAngleRad = (startAngle * Math.PI) / 180;
      const endAngleRad = (endAngle * Math.PI) / 180;

      // Generate section using high-performance renderer
      const sectionId = `arc-section-${Date.now()}`;
      const section = ShapeSeatGenerator.generateArcSeats(
        sectionId,
        x,
        y,
        {
          radius,
          startAngle: startAngleRad,
          endAngle: endAngleRad,
          rows,
          seatWidth: SEAT_SIZE,
          seatHeight: SEAT_SIZE,
          seatSpacing: SEAT_GAP,
          rowSpacing: SEAT_GAP,
          padding: SHAPE_PADDING,
        }
      );

      section.name = name;
      seatRenderer.addSection(section);

      // Reset form
      form.reset();
    } catch (e) {
      console.error("Error adding arc section:", e);
    }
  };

  return (
    <Form {...form}>
      <form className="flex w-full flex-col justify-center gap-4">
        <Name
          form={form}
          handleChange={(value) => form.setValue("name", value)}
          placeholder="Arc section name"
          showIcon={false}
        />
        
        <CustomNumberInput
          label="Radius (px)"
          key="radius"
          value={form.watch("radius")}
          onChange={(value) => form.setValue("radius", value)}
          min={50}
          max={1000}
          step={10}
        />

        <div className="grid grid-cols-2 gap-2">
          <CustomNumberInput
            label="Start Angle (°)"
            key="startAngle"
            value={form.watch("startAngle")}
            onChange={(value) => form.setValue("startAngle", value)}
            min={0}
            max={360}
            step={5}
          />
          
          <CustomNumberInput
            label="End Angle (°)"
            key="endAngle"
            value={form.watch("endAngle")}
            onChange={(value) => form.setValue("endAngle", value)}
            min={0}
            max={360}
            step={5}
          />
        </div>

        <CustomNumberInput
          label="Rows"
          key="rows"
          value={form.watch("rows")}
          onChange={(value) => form.setValue("rows", value)}
          min={1}
          max={50}
          step={1}
        />

        <Button onClick={onSubmit} className="w-full">
          Add Arc Section
        </Button>

        {nameError && (
          <Disclaimer variant="error" className="text-xs">
            {nameError}
          </Disclaimer>
        )}
        
        <Disclaimer variant="info" className="text-xs">
          Arc sections create curved seating arrangements. Seats are distributed along concentric arcs.
        </Disclaimer>
      </form>
    </Form>
  );
}
