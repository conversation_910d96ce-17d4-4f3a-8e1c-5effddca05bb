"use client";
import { useEffect, useState, useRef } from "react";
import { useSeatMapContext } from "../context";
import { useSeatRenderer } from "../hooks/useSeatRenderer";
import Button from "@components/primitive/Button";

export default function SeatMapControls() {
  const { layerRef, stageRef } = useSeatMapContext();
  const seatRenderer = useSeatRenderer({
    layer: layerRef.current,
    stage: stageRef.current,
  });

  const [stats, setStats] = useState({
    sections: 0,
    totalSeats: 0,
    activeSeats: 0,
    selectedSeats: 0,
  });

  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Update stats periodically
  useEffect(() => {
    const updateStats = () => {
      try {
        if (seatRenderer && typeof seatRenderer.getSectionCount === 'function') {
          const selectedSeats = seatRenderer.getSelectedSeats();
          setStats({
            sections: seatRenderer.getSectionCount(),
            totalSeats: seatRenderer.getTotalSeatCount(),
            activeSeats: seatRenderer.getActiveSeatCount(),
            selectedSeats: selectedSeats.length,
          });
        }
      } catch (error) {
        console.warn('Error updating stats:', error);
      }
    };

    // Clear existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    updateStats();
    intervalRef.current = setInterval(updateStats, 2000); // Reduced frequency

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []); // Remove dependencies to prevent infinite loop

  const handleClearSelection = () => {
    seatRenderer.clearSelection();
  };



  const handleZoomToFit = () => {
    if (!stageRef.current) return;

    const stage = stageRef.current;

    // Reset to default view
    stage.position({ x: 0, y: 0 });
    stage.scale({ x: 1, y: 1 });
    stage.batchDraw();
  };

  return (
    <div className="bg-white border-b border-gray-200 p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-6">
          <div className="text-sm">
            <span className="font-medium">Sections:</span> {stats.sections}
          </div>
          <div className="text-sm">
            <span className="font-medium">Total Seats:</span> {stats.totalSeats.toLocaleString()}
          </div>
          <div className="text-sm">
            <span className="font-medium">Active Seats:</span> {stats.activeSeats.toLocaleString()}
          </div>
          <div className="text-sm">
            <span className="font-medium">Selected:</span> {stats.selectedSeats}
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outlineLight"
            size="sm"
            onClick={handleClearSelection}
            disabled={stats.selectedSeats === 0}
            text="Clear Selection"
          />

          <Button
            variant="outlineLight"
            size="sm"
            onClick={handleZoomToFit}
            text="Reset View"
          />
        </div>
      </div>

    </div>
  );
}
