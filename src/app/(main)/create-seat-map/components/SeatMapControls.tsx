"use client";
import { useEffect, useState, useRef } from "react";
import { useSeatMapContext } from "../context";
import { useSeatRenderer } from "../hooks/useSeatRenderer";
import Button from "@components/primitive/Button";

export default function SeatMapControls() {
  const { layerRef, stageRef } = useSeatMapContext();
  const seatRenderer = useSeatRenderer({
    layer: layerRef.current,
    stage: stageRef.current,
  });

  const [stats, setStats] = useState({
    sections: 0,
    totalSeats: 0,
    activeSeats: 0,
    selectedSeats: 0,
  });

  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Update stats periodically
  useEffect(() => {
    const updateStats = () => {
      try {
        if (seatRenderer && typeof seatRenderer.getSectionCount === 'function') {
          const selectedSeats = seatRenderer.getSelectedSeats();
          setStats({
            sections: seatRenderer.getSectionCount(),
            totalSeats: seatRenderer.getTotalSeatCount(),
            activeSeats: seatRenderer.getActiveSeatCount(),
            selectedSeats: selectedSeats.length,
          });
        }
      } catch (error) {
        console.warn('Error updating stats:', error);
      }
    };

    // Clear existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    updateStats();
    intervalRef.current = setInterval(updateStats, 2000); // Reduced frequency

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []); // Remove dependencies to prevent infinite loop

  const handleClearSelection = () => {
    seatRenderer.clearSelection();
  };

  const handleTestPerformance = () => {
    // Create a large test section to demonstrate performance
    const testSection = {
      id: `performance-test-${Date.now()}`,
      name: "Performance Test Section",
      x: 100,
      y: 100,
      width: 2000,
      height: 2000,
      shape: 'rectangle' as const,
      seats: [] as any[],
    };

    // Generate 50,000 seats for performance testing
    const seatSize = 8;
    const seatGap = 2;
    const cols = 250;
    const rows = 200;

    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        testSection.seats.push({
          id: `test-seat-${row}-${col}`,
          x: testSection.x + col * (seatSize + seatGap),
          y: testSection.y + row * (seatSize + seatGap),
          width: seatSize,
          height: seatSize,
          row: row + 1,
          seatNumber: col + 1,
          isActive: true,
          isSelected: false,
          isHovered: false,
          label: `${row + 1}-${col + 1}`,
          sectionId: testSection.id,
        });
      }
    }

    console.log(`Adding ${testSection.seats.length} seats for performance test`);
    const startTime = performance.now();
    seatRenderer.addSection(testSection);
    const endTime = performance.now();
    console.log(`Performance test completed in ${endTime - startTime}ms`);
  };

  const handleZoomToFit = () => {
    if (!stageRef.current) return;

    const stage = stageRef.current;
    const padding = 50;
    
    // Reset stage position and scale
    stage.position({ x: 0, y: 0 });
    stage.scale({ x: 1, y: 1 });
    
    // Center the stage
    const stageWidth = stage.width();
    const stageHeight = stage.height();
    const contentWidth = 1000; // Approximate content width
    const contentHeight = 1000; // Approximate content height
    
    const scaleX = (stageWidth - padding * 2) / contentWidth;
    const scaleY = (stageHeight - padding * 2) / contentHeight;
    const scale = Math.min(scaleX, scaleY, 1); // Don't zoom in beyond 100%
    
    stage.scale({ x: scale, y: scale });
    stage.position({
      x: (stageWidth - contentWidth * scale) / 2,
      y: (stageHeight - contentHeight * scale) / 2,
    });
    
    stage.batchDraw();
  };

  return (
    <div className="bg-white border-b border-gray-200 p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-6">
          <div className="text-sm">
            <span className="font-medium">Sections:</span> {stats.sections}
          </div>
          <div className="text-sm">
            <span className="font-medium">Total Seats:</span> {stats.totalSeats.toLocaleString()}
          </div>
          <div className="text-sm">
            <span className="font-medium">Active Seats:</span> {stats.activeSeats.toLocaleString()}
          </div>
          <div className="text-sm">
            <span className="font-medium">Selected:</span> {stats.selectedSeats}
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outlineLight"
            size="sm"
            onClick={handleClearSelection}
            disabled={stats.selectedSeats === 0}
            text="Clear Selection"
          />

          <Button
            variant="outlineLight"
            size="sm"
            onClick={handleZoomToFit}
            text="Zoom to Fit"
          />

          <Button
            variant="outlineLight"
            size="sm"
            onClick={handleTestPerformance}
            text="Test Performance (50k seats)"
          />
        </div>
      </div>
      
      {stats.totalSeats > 10000 && (
        <div className="mt-2 text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
          ⚡ High-performance rendering active for {stats.totalSeats.toLocaleString()} seats
        </div>
      )}
    </div>
  );
}
