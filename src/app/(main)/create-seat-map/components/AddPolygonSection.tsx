import Disclaimer from "@components/primitive/Disclaimer";
import { CustomNumberInput } from "@components/primitive/CustomNumberInput";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@components/shadcn/Form";
import Name from "@components/form/Name";
import Button from "@components/primitive/Button";
import { useSeatMapContext } from "../context";
import useLayout from "../hooks/useLayout";
import { SEAT_SIZE, SEAT_GAP, SHAPE_PADDING } from "../lib/config";
import { ShapeSeatGenerator } from "../lib/ShapeSeats";
import { useSeatRenderer } from "../hooks/useSeatRenderer";
import { useState } from "react";

const FormSchema = z.object({
  name: z.string().min(1, { message: "Name is required" }),
  capacity: z
    .number()
    .min(1, { message: "At least 1 seat required" })
    .max(10000, { message: "Max. 10,000 seats allowed" }),
});

export default function AddPolygonSection() {
  const { layerRef, stageRef, dragLayerRef } = useSeatMapContext();
  const { getInsertPosition } = useLayout();
  const seatRenderer = useSeatRenderer({
    layer: layerRef.current,
    stage: stageRef.current,
    dragLayer: dragLayerRef.current,
  });

  const [polygonPoints, setPolygonPoints] = useState<number[]>([]);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: { 
      name: "", 
      capacity: 100,
    },
  });

  const formErrors = form.formState.errors;
  const nameError = formErrors.name?.message;

  const handleCreateTriangle = () => {
    const { x, y } = getInsertPosition();
    const size = 200;
    const points = [
      x + size / 2, y,           // Top point
      x, y + size,               // Bottom left
      x + size, y + size,        // Bottom right
    ];
    setPolygonPoints(points);
  };

  const handleCreatePentagon = () => {
    const { x, y } = getInsertPosition();
    const radius = 150;
    const centerX = x + radius;
    const centerY = y + radius;
    const points: number[] = [];
    
    for (let i = 0; i < 5; i++) {
      const angle = (i * 2 * Math.PI) / 5 - Math.PI / 2; // Start from top
      const pointX = centerX + radius * Math.cos(angle);
      const pointY = centerY + radius * Math.sin(angle);
      points.push(pointX, pointY);
    }
    setPolygonPoints(points);
  };

  const handleCreateHexagon = () => {
    const { x, y } = getInsertPosition();
    const radius = 150;
    const centerX = x + radius;
    const centerY = y + radius;
    const points: number[] = [];
    
    for (let i = 0; i < 6; i++) {
      const angle = (i * 2 * Math.PI) / 6;
      const pointX = centerX + radius * Math.cos(angle);
      const pointY = centerY + radius * Math.sin(angle);
      points.push(pointX, pointY);
    }
    setPolygonPoints(points);
  };

  const handleCreateStadiumSection = () => {
    const { x, y } = getInsertPosition();
    const width = 300;
    const height = 200;
    // Create a stadium-like shape (rectangle with rounded ends)
    const points = [
      x + 50, y,
      x + width - 50, y,
      x + width, y + 50,
      x + width, y + height - 50,
      x + width - 50, y + height,
      x + 50, y + height,
      x, y + height - 50,
      x, y + 50,
    ];
    setPolygonPoints(points);
  };

  const onSubmit = async () => {
    try {
      if (polygonPoints.length < 6) { // At least 3 points (6 coordinates)
        form.setError("capacity", { message: "Please create a polygon shape first" });
        return;
      }

      const name = form.getValues("name");
      const capacity = form.getValues("capacity");

      // Generate section using high-performance renderer
      const sectionId = `polygon-section-${Date.now()}`;
      const section = ShapeSeatGenerator.generatePolygonSeats(
        sectionId,
        {
          points: polygonPoints,
          capacity,
          seatWidth: SEAT_SIZE,
          seatHeight: SEAT_SIZE,
          seatSpacing: SEAT_GAP,
          rowSpacing: SEAT_GAP,
          padding: SHAPE_PADDING,
        }
      );

      section.name = name;
      seatRenderer.addSection(section);

      // Reset form and polygon
      form.reset();
      setPolygonPoints([]);
    } catch (e) {
      console.error("Error adding polygon section:", e);
    }
  };

  const clearPolygon = () => {
    setPolygonPoints([]);
  };

  return (
    <Form {...form}>
      <div className="flex w-full flex-col justify-center gap-4">
        <Name
          form={form}
          handleChange={(value) => form.setValue("name", value)}
          placeholder="Polygon section name"
          showIcon={false}
        />
        
        <CustomNumberInput
          label="Seat Capacity"
          key="capacity"
          value={form.watch("capacity")}
          onChange={(value) => form.setValue("capacity", value)}
          min={1}
          max={10000}
        />

        <div className="space-y-2">
          <label className="text-sm font-medium">Quick Shapes:</label>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outlineLight"
              size="sm"
              onClick={handleCreateTriangle}
              text="Triangle"
            />
            <Button
              variant="outlineLight"
              size="sm"
              onClick={handleCreatePentagon}
              text="Pentagon"
            />
            <Button
              variant="outlineLight"
              size="sm"
              onClick={handleCreateHexagon}
              text="Hexagon"
            />
            <Button
              variant="outlineLight"
              size="sm"
              onClick={handleCreateStadiumSection}
              text="Stadium"
            />
          </div>
        </div>

        {polygonPoints.length > 0 && (
          <div className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
            ✓ Polygon created with {polygonPoints.length / 2} points
          </div>
        )}

        <div className="flex gap-2">
          <Button
            onClick={onSubmit}
            fullWidth
            disabled={polygonPoints.length < 6}
            text="Add Polygon Section"
          />

          {polygonPoints.length > 0 && (
            <Button
              variant="outlineLight"
              onClick={clearPolygon}
              text="Clear"
            />
          )}
        </div>

        {nameError && (
          <Disclaimer message={nameError} variant="destructive" />
        )}

        <Disclaimer
          message="Polygon sections allow custom shapes. Use quick shapes or draw your own polygon on the canvas."
          variant="default"
        />
      </div>
    </Form>
  );
}
