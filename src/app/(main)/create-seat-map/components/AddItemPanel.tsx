"use client";
import { SidePanelHeader } from "@components/builder/PanelHeader";
import { useSeatMapContext } from "../context";
import useSeatMapStore from "@stores/useSeatMapStore";
import { cn } from "@lib/utils";
import { getPanelHeaderName } from "../lib/panelHeader";
import AddSeatedSection from "./AddSeatedSection";
import AddArcSection from "./AddArcSection";
import AddPolygonSection from "./AddPolygonSection";
import AddCircleSection from "./AddCircleSection";

export default function AddItemPanel() {
  const { newItemView, setNewItemView } = useSeatMapStore();

  return (
    <div
      className={cn(
        "absolute left-0 top-0 flex h-full w-[400px] flex-shrink-0 flex-col border-gray-200 bg-white",
        !!newItemView ? "z-10" : "-z-10",
      )}
    >
      <SidePanelHeader
        title={getPanelHeaderName(newItemView)}
        onBackClick={() => setNewItemView(null)}
        backTitle="Back"
      />
      <div className="flex flex-col gap-8 overflow-y-auto px-5 py-4">
        {newItemView === "seated-section" && <AddSeatedSection />}
        {newItemView === "arc-section" && <AddArcSection />}
        {newItemView === "polygon-section" && <AddPolygonSection />}
        {newItemView === "circle-section" && <AddCircleSection />}
      </div>
    </div>
  );
}
