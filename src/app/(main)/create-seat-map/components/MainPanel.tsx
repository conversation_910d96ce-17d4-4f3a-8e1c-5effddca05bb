"use client";
import { SidePanelHeader } from "@components/builder/PanelHeader";
import { useSeatMapContext } from "../context";
import AddItemButton from "./AddItemButton";
import DotsCircleIcon from "mdi-react/DotsCircleIcon";
import AccountGroupIcon from "mdi-react/AccountGroupIcon";
import VectorRectangleIcon from "mdi-react/VectorRectangleIcon";
import DotsSquareIcon from "mdi-react/DotsSquareIcon";
import SignDirectionIcon from "mdi-react/SignDirectionIcon";
import VectorPolygonIcon from "mdi-react/VectorPolygonIcon";
import CircleIcon from "mdi-react/CircleIcon";
import VectorCurveIcon from "mdi-react/VectorCurveIcon";
import NameForm from "./NameForm";
import useSeatMapStore from "@stores/useSeatMapStore";

export default function MainPanel() {
  const { setNewItemView } = useSeatMapStore();
  const { stageRef, layerRef } = useSeatMapContext();

  return (
    <div className="absolute left-0 top-0 z-0 flex h-full w-[400px] flex-shrink-0 flex-col border-gray-200 bg-white">
      <SidePanelHeader title={"Your Seat Map"} />
      <div className="flex flex-col gap-8 overflow-y-auto px-5 py-4">
        <NameForm />
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-2">Seating Sections</h3>
            <div className="grid w-full grid-cols-2 gap-2">
              <AddItemButton id="seated-section">
                <VectorRectangleIcon className="h-6 w-6 text-dark100" />
              </AddItemButton>
              <AddItemButton id="arc-section">
                <VectorCurveIcon className="h-6 w-6 text-dark100" />
              </AddItemButton>
              <AddItemButton id="polygon-section">
                <VectorPolygonIcon className="h-6 w-6 text-dark100" />
              </AddItemButton>
              <AddItemButton id="circle-section">
                <CircleIcon className="h-6 w-6 text-dark100" />
              </AddItemButton>
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-2">Other Elements</h3>
            <div className="grid w-full grid-cols-2 gap-2">
              <AddItemButton id="standing-section">
                <AccountGroupIcon className="h-6 w-6 text-dark100" />
              </AddItemButton>
              <AddItemButton id="round-table">
                <DotsCircleIcon className="h-6 w-6 text-dark100" />
              </AddItemButton>
              <AddItemButton id="rectangle-table">
                <DotsSquareIcon className="h-6 w-6 text-dark100" />
              </AddItemButton>
              <AddItemButton id="landmark">
                <SignDirectionIcon className="h-6 w-6 text-dark100" />
              </AddItemButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
