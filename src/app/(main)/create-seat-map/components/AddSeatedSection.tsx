import Disclaimer from "@components/primitive/Disclaimer";
import { CustomNumberInput } from "@components/primitive/CustomNumberInput";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@components/shadcn/Form";
import Name from "@components/form/Name";
import Button from "@components/primitive/Button";
import { useSeatMapContext } from "../context";
import useLayout from "../hooks/useLayout";
import { SEAT_SIZE, SEAT_GAP, SHAPE_PADDING } from "../lib/config";
import { ShapeSeatGenerator } from "../lib/ShapeSeats";
import { useSeatRenderer } from "../hooks/useSeatRenderer";

const FormSchema = z.object({
  name: z.string().min(4, { message: "Name must be at least 4 char." }),
  rows: z
    .number()
    .min(1, { message: "At least 1 row required" })
    .max(1000, { message: "Max. 1000 rows allowed" }),
  seatsPerRow: z
    .number()
    .min(1, { message: "At least 1 seat required" })
    .max(1000, { message: "Max. 1000 seats per row allowed" }),
});

export default function AddSeatedSection() {
  const { layerRef } = useSeatMapContext();
  const { getInsertPosition } = useLayout();
  const seatRenderer = useSeatRenderer({
    layer: layerRef.current,
    stage: null,
  });

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: { name: "", rows: 10, seatsPerRow: 10 },
  });

  const formErrors = form.formState.errors;
  const nameError = formErrors.name?.message;

  const onSubmit = async () => {
    try {
      const { x, y } = getInsertPosition();
      const name = form.getValues("name");
      const rows = form.getValues("rows");
      const seatsPerRow = form.getValues("seatsPerRow");

      // Generate section using high-performance renderer
      const sectionId = `section-${Date.now()}`;
      const section = ShapeSeatGenerator.generateRectangleSeats(
        sectionId,
        x,
        y,
        {
          rows,
          seatsPerRow,
          seatWidth: SEAT_SIZE,
          seatHeight: SEAT_SIZE,
          seatSpacing: SEAT_GAP,
          rowSpacing: SEAT_GAP,
          padding: SHAPE_PADDING,
        }
      );

      section.name = name;
      seatRenderer.addSection(section);

      // Reset form
      form.reset();
    } catch (e) {
      console.error("Error adding seated section:", e);
    }
  };

  return (
    <Form {...form}>
      <form className="flex w-full flex-col justify-center gap-4">
        <Name
          form={form}
          handleChange={(value) => form.setValue("name", value)}
          placeholder="Section name"
          showIcon={false}
        />
        <CustomNumberInput
          label="Rows"
          key="numberOfRows"
          value={form.watch("rows")}
          min={1}
          max={1000}
          onChange={(value) => form.setValue("rows", value)}
        />
        <CustomNumberInput
          label="Seats per row"
          key="seatsPerRow"
          value={form.watch("seatsPerRow")}
          min={1}
          max={1000}
          onChange={(value) => form.setValue("seatsPerRow", value)}
        />
        <Disclaimer message={nameError} variant="destructive" />
        <div className="flex justify-between">
          <Button fullWidth text="Add" onClick={form.handleSubmit(onSubmit)} />
        </div>
      </form>
    </Form>
  );
}
