import Disclaimer from "@components/primitive/Disclaimer";
import { CustomNumberInput } from "@components/primitive/CustomNumberInput";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@components/shadcn/Form";
import Name from "@components/form/Name";
import Button from "@components/primitive/Button";
import { useSeatMapContext } from "../context";
import useLayout from "../hooks/useLayout";
import { SEAT_SIZE, SEAT_GAP, SHAPE_PADDING } from "../lib/config";
import { ShapeSeatGenerator } from "../lib/ShapeSeats";
import { useSeatRenderer } from "../hooks/useSeatRenderer";

const FormSchema = z.object({
  name: z.string().min(4, { message: "Name must be at least 4 char." }),
  rows: z
    .number()
    .min(1, { message: "At least 1 row required" })
    .max(1000, { message: "Max. 1000 rows allowed" }),
  seatsPerRow: z
    .number()
    .min(1, { message: "At least 1 seat required" })
    .max(1000, { message: "Max. 1000 seats per row allowed" }),
});

export default function AddSeatedSection() {
  const { layerRef, stageRef, dragLayerRef } = useSeatMapContext();
  const { getInsertPosition } = useLayout();
  const seatRenderer = useSeatRenderer({
    layer: layerRef.current,
    stage: stageRef.current,
    dragLayer: dragLayerRef.current,
  });

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: { name: "", rows: 10, seatsPerRow: 10 },
  });

  const formErrors = form.formState.errors;
  const nameError = formErrors.name?.message;

  const onSubmit = async (data: any, event?: React.BaseSyntheticEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    try {
      if (!layerRef.current) {
        console.error("Layer not available");
        return;
      }

      const { x, y } = getInsertPosition();
      const { name, rows, seatsPerRow } = data;

      // Performance limit: prevent crashes from too many seats
      const totalSeats = rows * seatsPerRow;
      if (totalSeats > 50000) {
        alert(`Too many seats! ${totalSeats.toLocaleString()} seats would crash the browser. Please use fewer than 50,000 seats per section.`);
        return;
      }

      // Generate section using high-performance renderer
      const sectionId = `section-${Date.now()}`;
      const section = ShapeSeatGenerator.generateRectangleSeats(
        sectionId,
        x,
        y,
        {
          rows,
          seatsPerRow,
          seatWidth: SEAT_SIZE,
          seatHeight: SEAT_SIZE,
          seatSpacing: SEAT_GAP,
          rowSpacing: SEAT_GAP,
          padding: SHAPE_PADDING,
        }
      );

      section.name = name;
      seatRenderer.addSection(section);

      // Reset form
      form.reset();
    } catch (e) {
      console.error("Error adding seated section:", e);
    }
  };

  return (
    <Form {...form}>
      <div className="flex w-full flex-col justify-center gap-4">
        <Name
          form={form}
          handleChange={(value) => form.setValue("name", value)}
          placeholder="Section name"
          showIcon={false}
        />
        <CustomNumberInput
          label="Rows"
          key="numberOfRows"
          value={form.watch("rows")}
          min={1}
          max={1000}
          onChange={(value) => form.setValue("rows", value)}
        />
        <CustomNumberInput
          label="Seats per row"
          key="seatsPerRow"
          value={form.watch("seatsPerRow")}
          min={1}
          max={1000}
          onChange={(value) => form.setValue("seatsPerRow", value)}
        />
        <Disclaimer message={nameError} variant="destructive" />
        <div className="flex justify-between">
          <Button
            fullWidth
            text="Add Seated Section"
            onClick={() => {
              form.handleSubmit(onSubmit)();
            }}
          />
        </div>
      </div>
    </Form>
  );
}
