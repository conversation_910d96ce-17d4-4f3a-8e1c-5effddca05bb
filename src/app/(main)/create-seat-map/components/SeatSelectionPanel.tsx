"use client";
import { useState, useEffect } from "react";
import { useSeatMapContext } from "../context";
import { useSeatRenderer } from "../hooks/useSeatRenderer";
import { Seat } from "../lib/SeatRenderer";
import Button from "@components/primitive/Button";

export default function SeatSelectionPanel() {
  const { layerRef, stageRef } = useSeatMapContext();
  const seatRenderer = useSeatRenderer({
    layer: layerRef.current,
    stage: stageRef.current,
  });

  const [selectedSeats, setSelectedSeats] = useState<Seat[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const updateSelection = () => {
      try {
        const seats = seatRenderer.getSelectedSeats();
        setSelectedSeats(seats);
        setIsVisible(seats.length > 0);
      } catch (error) {
        console.warn('Error updating selection:', error);
      }
    };

    const interval = setInterval(updateSelection, 500);
    return () => clearInterval(interval);
  }, []);

  const handleClearSelection = () => {
    seatRenderer.clearSelection();
    setSelectedSeats([]);
    setIsVisible(false);
  };

  const handleToggleSeatActive = (seatId: string) => {
    seatRenderer.toggleSeatActive(seatId);
    // Update the selected seats list to reflect changes
    setTimeout(() => {
      const updatedSeats = seatRenderer.getSelectedSeats();
      setSelectedSeats(updatedSeats);
    }, 100);
  };

  const handleUpdateSeatLabel = (seatId: string, newLabel: string) => {
    seatRenderer.updateSeatLabel(seatId, newLabel);
    // Update the selected seats list to reflect changes
    setTimeout(() => {
      const updatedSeats = seatRenderer.getSelectedSeats();
      setSelectedSeats(updatedSeats);
    }, 100);
  };

  const groupSeatsBySection = (seats: Seat[]) => {
    const grouped: { [sectionId: string]: Seat[] } = {};
    seats.forEach(seat => {
      if (!grouped[seat.sectionId]) {
        grouped[seat.sectionId] = [];
      }
      grouped[seat.sectionId].push(seat);
    });
    return grouped;
  };

  if (!isVisible) {
    return null;
  }

  const groupedSeats = groupSeatsBySection(selectedSeats);

  return (
    <div className="absolute right-4 top-20 z-10 w-80 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-h-96 overflow-y-auto">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Selected Seats ({selectedSeats.length})</h3>
        <Button
          variant="outlineLight"
          size="sm"
          onClick={handleClearSelection}
          text="Clear All"
        />
      </div>

      <div className="space-y-4">
        {Object.entries(groupedSeats).map(([sectionId, seats]) => (
          <div key={sectionId} className="border-b border-gray-100 pb-3">
            <h4 className="font-medium text-gray-700 mb-2">
              Section: {sectionId} ({seats.length} seats)
            </h4>
            
            <div className="space-y-2">
              {seats.slice(0, 10).map((seat) => (
                <div key={seat.id} className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2">
                    <div 
                      className={`w-4 h-4 rounded border ${
                        seat.isActive 
                          ? 'bg-blue-500 border-blue-600' 
                          : 'bg-gray-300 border-gray-400'
                      }`}
                    />
                    <span className={seat.isActive ? 'text-gray-900' : 'text-gray-500'}>
                      {seat.label || `${seat.row}-${seat.seatNumber}`}
                    </span>
                  </div>
                  
                  <div className="flex gap-1">
                    <button
                      onClick={() => handleToggleSeatActive(seat.id)}
                      className={`px-2 py-1 text-xs rounded ${
                        seat.isActive
                          ? 'bg-red-100 text-red-700 hover:bg-red-200'
                          : 'bg-green-100 text-green-700 hover:bg-green-200'
                      }`}
                    >
                      {seat.isActive ? 'Deactivate' : 'Activate'}
                    </button>
                    
                    <button
                      onClick={() => {
                        const newLabel = prompt('Enter new label:', seat.label || '');
                        if (newLabel !== null) {
                          handleUpdateSeatLabel(seat.id, newLabel);
                        }
                      }}
                      className="px-2 py-1 text-xs bg-blue-100 text-blue-700 hover:bg-blue-200 rounded"
                    >
                      Edit
                    </button>
                  </div>
                </div>
              ))}
              
              {seats.length > 10 && (
                <div className="text-xs text-gray-500 italic">
                  ... and {seats.length - 10} more seats
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 pt-3 border-t border-gray-200">
        <div className="text-sm text-gray-600">
          <div className="flex justify-between">
            <span>Total Selected:</span>
            <span className="font-medium">{selectedSeats.length}</span>
          </div>
          <div className="flex justify-between">
            <span>Active:</span>
            <span className="font-medium text-green-600">
              {selectedSeats.filter(s => s.isActive).length}
            </span>
          </div>
          <div className="flex justify-between">
            <span>Inactive:</span>
            <span className="font-medium text-red-600">
              {selectedSeats.filter(s => !s.isActive).length}
            </span>
          </div>
        </div>
      </div>

      <div className="mt-3 text-xs text-gray-500">
        💡 Click seats on the canvas to select/deselect them. Use Ctrl+Click for multi-selection.
      </div>
    </div>
  );
}
