import Disclaimer from "@components/primitive/Disclaimer";
import { CustomNumberInput } from "@components/primitive/CustomNumberInput";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@components/shadcn/Form";
import Name from "@components/form/Name";
import Button from "@components/primitive/Button";
import { useSeatMapContext } from "../context";
import useLayout from "../hooks/useLayout";
import { SEAT_SIZE, SEAT_GAP, SHAPE_PADDING } from "../lib/config";
import { ShapeSeatGenerator } from "../lib/ShapeSeats";
import { useSeatRenderer } from "../hooks/useSeatRenderer";

const FormSchema = z.object({
  name: z.string().min(1, { message: "Name is required" }),
  radius: z
    .number()
    .min(50, { message: "Minimum radius is 50px" })
    .max(500, { message: "Maximum radius is 500px" }),
  capacity: z
    .number()
    .min(1, { message: "At least 1 seat required" })
    .max(5000, { message: "Max. 5,000 seats allowed" }),
});

export default function AddCircleSection() {
  const { layerRef } = useSeatMapContext();
  const { getInsertPosition } = useLayout();
  const seatRenderer = useSeatRenderer({
    layer: layerRef.current,
    stage: null,
  });

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: { 
      name: "", 
      radius: 150, 
      capacity: 200,
    },
  });

  const formErrors = form.formState.errors;
  const nameError = formErrors.name?.message;

  // Calculate estimated capacity based on radius
  const estimatedCapacity = () => {
    const radius = form.watch("radius");
    const area = Math.PI * radius * radius;
    const seatArea = SEAT_SIZE * SEAT_SIZE;
    const efficiency = 0.6; // Circular packing efficiency
    return Math.floor((area * efficiency) / seatArea);
  };

  const onSubmit = async () => {
    try {
      const { x, y } = getInsertPosition();
      const name = form.getValues("name");
      const radius = form.getValues("radius");
      const capacity = form.getValues("capacity");

      // Validate capacity against estimated maximum
      const maxCapacity = estimatedCapacity();
      if (capacity > maxCapacity * 1.2) { // Allow 20% over estimate
        form.setError("capacity", { 
          message: `Capacity too high for radius. Estimated max: ${maxCapacity}` 
        });
        return;
      }

      // Generate section using high-performance renderer
      const sectionId = `circle-section-${Date.now()}`;
      const centerX = x + radius;
      const centerY = y + radius;
      
      const section = ShapeSeatGenerator.generateCircleSeats(
        sectionId,
        centerX,
        centerY,
        {
          radius,
          capacity,
          seatWidth: SEAT_SIZE,
          seatHeight: SEAT_SIZE,
          seatSpacing: SEAT_GAP,
          rowSpacing: SEAT_GAP,
          padding: SHAPE_PADDING,
        }
      );

      section.name = name;
      seatRenderer.addSection(section);

      // Reset form
      form.reset();
    } catch (e) {
      console.error("Error adding circle section:", e);
    }
  };

  return (
    <Form {...form}>
      <form className="flex w-full flex-col justify-center gap-4">
        <Name
          form={form}
          handleChange={(value) => form.setValue("name", value)}
          placeholder="Circle section name"
          showIcon={false}
        />
        
        <CustomNumberInput
          label="Radius (px)"
          key="radius"
          value={form.watch("radius")}
          onChange={(value) => form.setValue("radius", value)}
          min={50}
          max={500}
          step={10}
        />

        <CustomNumberInput
          label="Seat Capacity"
          key="capacity"
          value={form.watch("capacity")}
          onChange={(value) => form.setValue("capacity", value)}
          min={1}
          max={5000}
          step={10}
        />

        <div className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
          💡 Estimated capacity for this radius: ~{estimatedCapacity()} seats
        </div>

        <Button onClick={onSubmit} className="w-full">
          Add Circle Section
        </Button>

        {nameError && (
          <Disclaimer variant="error" className="text-xs">
            {nameError}
          </Disclaimer>
        )}
        
        <Disclaimer variant="info" className="text-xs">
          Circle sections arrange seats in concentric rings. Perfect for amphitheaters and round venues.
        </Disclaimer>
      </form>
    </Form>
  );
}
