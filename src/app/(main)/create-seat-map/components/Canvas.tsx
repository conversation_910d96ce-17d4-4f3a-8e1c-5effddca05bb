"use client";
import Konva from "konva";
import { Stage, Layer, Transformer } from "react-konva";
import ZoomLevel from "./ZoomLevel";
import { useSeatMapContext } from "../context";
import useSeatMapStore from "@stores/useSeatMapStore";
import LayoutInfo from "./LayoutInfo";
import useDimensionsListener from "../hooks/useDimensionsListener";
import { maintainSquare } from "../lib/dimensions";
import CanvasLayout from "./CanvasLayout";
import { useSeatRenderer } from "../hooks/useSeatRenderer";
import { useEffect } from "react";

export default function Canvas() {
  useDimensionsListener();
  const { stageRef, layerRef, dragLayerRef, transformerRef } = useSeatMapContext();
  const { stageScale, stageDimensions, layoutDimensions, setLayoutDimensions } =
    useSeatMapStore();

  // Initialize high-performance seat renderer
  const seatRenderer = useSeatRenderer({
    layer: layerRef.current,
    stage: stageRef.current,
  });



  // Update viewport when stage changes
  useEffect(() => {
    const updateViewport = () => {
      if (stageRef.current) {
        const stage = stageRef.current;
        const viewport = {
          x: -stage.x() / stage.scaleX(),
          y: -stage.y() / stage.scaleY(),
          width: stage.width() / stage.scaleX(),
          height: stage.height() / stage.scaleY(),
          scale: stage.scaleX(),
        };
        seatRenderer.updateViewport(viewport);
      }
    };

    if (stageRef.current) {
      const stage = stageRef.current;
      stage.on('dragmove', updateViewport);
      stage.on('wheel', updateViewport);
      updateViewport(); // Initial update

      return () => {
        stage.off('dragmove', updateViewport);
        stage.off('wheel', updateViewport);
      };
    }
  }, [stageRef.current, seatRenderer]);

  const handleWheel = (e: Konva.KonvaEventObject<WheelEvent>) => {
    e.evt.preventDefault();

    if (!stageRef.current) return;
    const stage = stageRef.current;
    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();
    if (!pointer) return;

    const mousePointTo = {
      x: (pointer.x - stage.x()) / oldScale,
      y: (pointer.y - stage.y()) / oldScale,
    };

    // how to scale? Zoom in? Or zoom out?
    let direction = e.evt.deltaY > 0 ? 1 : -1;

    // when we zoom on trackpad, e.evt.ctrlKey is true
    // in that case lets revert direction
    if (e.evt.ctrlKey) {
      direction = -direction;
    }

    const scaleBy = 1.05;
    const newScale = direction > 0 ? oldScale * scaleBy : oldScale / scaleBy;

    // Better zoom limits
    if (newScale < 0.1 || newScale >= 5) return;

    stage.scale({ x: newScale, y: newScale });

    const newPos = {
      x: pointer.x - mousePointTo.x * newScale,
      y: pointer.y - mousePointTo.y * newScale,
    };

    stage.position(newPos);
    stage.batchDraw();
  };

  const handleStageClick = (e: Konva.KonvaEventObject<MouseEvent>) => {
    const clickedId = e?.target.id();
    const nodes = transformerRef.current?.nodes() || [];
    const currentClickedId = nodes.length > 0 ? nodes[0].id() : null;
    const isAlreadySelected = currentClickedId === clickedId;

    const node = e.target;
    console.log(node);
    if (!node || node instanceof Konva.Stage || isAlreadySelected) {
      transformerRef.current?.nodes([]);
      setLayoutDimensions({ ...layoutDimensions, selected: false });
    } else {
      transformerRef.current?.nodes([node]);
      setLayoutDimensions({ ...layoutDimensions, selected: true });
    }
  };

  return (
    <div className="bg-gray-light100 relative flex h-full w-full items-center justify-center">
      <ZoomLevel stageScale={stageScale} />
      <LayoutInfo />
      <Stage
        ref={stageRef}
        className="border-l border-gray-200"
        width={stageDimensions.w}
        height={stageDimensions.h}
        onWheel={handleWheel}
        onClick={handleStageClick}
        draggable={true}
      >
        <Layer ref={layerRef} x={(stageDimensions.w - 500) / 2} y={(stageDimensions.h - 500) / 2}>
          <CanvasLayout />
          <Transformer keepRatio={true} boundBoxFunc={maintainSquare} ref={transformerRef} />
        </Layer>
        <Layer ref={dragLayerRef} x={(stageDimensions.w - 500) / 2} y={(stageDimensions.h - 500) / 2} />
      </Stage>
    </div>
  );
}
